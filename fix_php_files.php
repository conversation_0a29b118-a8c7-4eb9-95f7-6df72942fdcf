<?php
/**
 * <PERSON>ript to fix PHP 8.3 compatibility issues in the statistics web application
 */

function fixPhpFile($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix short PHP tags
    $content = preg_replace('/^<\?(?!\?php)/', '<?php', $content);
    $content = preg_replace('/(<\?(?!\?php))(?!\s*=)/', '<?php', $content);
    
    // Fix file handling functions - add quotes around file mode
    $content = preg_replace('/FOpen\s*\(\s*"([^"]+)"\s*,\s*r\s*\)/', 'fopen("$1","r")', $content);
    $content = preg_replace('/fopen\s*\(\s*"([^"]+)"\s*,\s*r\s*\)/', 'fopen("$1","r")', $content);
    
    // Fix function names to lowercase
    $content = preg_replace('/FOpen\s*\(/', 'fopen(', $content);
    $content = preg_replace('/FSeek\s*\(/', 'fseek(', $content);
    $content = preg_replace('/FRead\s*\(/', 'fread(', $content);
    $content = preg_replace('/FClose\s*\(/', 'fclose(', $content);
    
    // Fix HTML attributes - add quotes
    $content = preg_replace('/(<[^>]+\s)(method|type|name|value|size|bgcolor|link|alink|vlink|onunload|onmouseover)=([^"\s>]+)/', '$1$2="$3"', $content);
    
    // Fix echo statements
    $content = preg_replace('/<\?echo\s*\(([^)]+)\)\s*;\s*\?>/', '<?php echo($1);?>', $content);
    $content = preg_replace('/<\?echo\s*\(([^)]+)\)\s*\?>/', '<?php echo($1);?>', $content);
    
    // Fix input types
    $content = str_replace('type="integer"', 'type="number"', $content);
    $content = str_replace('type="double"', 'type="number" step="any"', $content);
    
    // Add HTML5 doctype if missing
    if (!preg_match('/<!DOCTYPE/i', $content)) {
        if (preg_match('/^(\s*)<html>/i', $content)) {
            $content = preg_replace('/^(\s*)<html>/i', '$1<!DOCTYPE html>\n$1<html>', $content);
        }
    }
    
    // Add charset meta tag if missing
    if (preg_match('/<head>/i', $content) && !preg_match('/<meta[^>]+charset/i', $content)) {
        $content = preg_replace('/(<head>)/i', '$1\n    <meta charset="UTF-8">', $content);
    }
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Fixed: $filePath\n";
        return true;
    }
    
    echo "No changes needed: $filePath\n";
    return false;
}

// Get all PHP files
$phpFiles = glob('statisticsonweb.tf.czu.cz/*.php');

echo "Found " . count($phpFiles) . " PHP files\n";
echo "Starting fixes...\n\n";

$fixedCount = 0;
foreach ($phpFiles as $file) {
    if (fixPhpFile($file)) {
        $fixedCount++;
    }
}

echo "\nCompleted! Fixed $fixedCount files.\n";
?>
