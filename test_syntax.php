<?php
/**
 * Test script to check PHP syntax of all files
 */

function testPhpSyntax($filePath) {
    $output = [];
    $returnCode = 0;
    
    // Use php -l to check syntax
    exec("php -l \"$filePath\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✓ PASS: $filePath\n";
        return true;
    } else {
        echo "✗ FAIL: $filePath\n";
        echo "  Error: " . implode("\n  ", $output) . "\n";
        return false;
    }
}

// Get all PHP files
$phpFiles = glob('statisticsonweb.tf.czu.cz/*.php');

echo "Testing PHP syntax for " . count($phpFiles) . " files...\n\n";

$passCount = 0;
$failCount = 0;

foreach ($phpFiles as $file) {
    if (testPhpSyntax($file)) {
        $passCount++;
    } else {
        $failCount++;
    }
}

echo "\n=== RESULTS ===\n";
echo "PASSED: $passCount files\n";
echo "FAILED: $failCount files\n";

if ($failCount === 0) {
    echo "\n🎉 All files have valid PHP syntax!\n";
} else {
    echo "\n⚠️  Some files have syntax errors that need fixing.\n";
}
?>
