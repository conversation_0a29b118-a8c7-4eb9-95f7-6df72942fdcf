<?php
/**
 * Script to fix remaining syntax errors
 */

function fixRemainingErrors($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix array assignment issues
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]\s*\?\?\s*\'\'\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]=/', '$$$1[$$2][$$3]=', $content);
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*\'\'\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]=/', '$$$1[$$2]=', $content);
    
    // Fix loop conditions with null coalescing
    $content = preg_replace('/\$k\s*<\s*\$s\[\$i\]\s*\?\?\s*\'\'/', '$k <$s[$i]', $content);
    $content = preg_replace('/\$k\s*<\s*\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]\s*\?\?\s*\'\'/', '$k <$$$1[$$2]', $content);
    
    // Fix double dollar signs in array access
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/', '$$$1[', $content);
    
    // Fix specific problematic patterns
    $content = preg_replace('/for\s*\([^)]*\$k\s*<\s*[^;]*\?\?\s*\'\'[^)]*\)/', 'for ($k=$s[$i-1]; $k<$s[$i]; $k++)', $content);
    
    // Fix URL encoding patterns
    $content = preg_replace('/echo\'([^\']*)\%5B\%5D=\',\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\]\s*\?\?\s*\'\',\'&\';/', 'echo\'$1%5B%5D=\',$$$2[$i],\'&\';', $content);
    
    // Fix echo statements in URLs
    $content = preg_replace('/echo\'([^\']*)\',\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*\'\',\'([^\']*)\';/', 'echo\'$1\',$$$2,\'$3\';', $content);
    
    // Fix ampersand issues in URLs
    $content = preg_replace('/&([a-zA-Z_][a-zA-Z0-9_]*)=\',\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*\'\',\'/', '&$1=\',$$$2,\'', $content);
    
    // Fix specific echo patterns
    $content = preg_replace('/echo\(\'([^\']*)\'\.\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*\'\'\.\'\'\);/', 'echo(\'$1\'.$$$2.\'\');', $content);
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Fixed remaining errors in: $filePath\n";
        return true;
    }
    
    echo "No remaining fixes needed: $filePath\n";
    return false;
}

// Get all PHP files
$phpFiles = glob('statisticsonweb.tf.czu.cz/*.php');

echo "Fixing remaining errors in " . count($phpFiles) . " files...\n\n";

$fixedCount = 0;
foreach ($phpFiles as $file) {
    if (fixRemainingErrors($file)) {
        $fixedCount++;
    }
}

echo "\nCompleted remaining fixes! Fixed $fixedCount files.\n";
?>
