<?php
/**
 * <PERSON>ript to fix specific syntax errors caused by incorrect regex replacements
 */

function fixSyntaxErrors($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix incorrect variable assignments with null coalescing
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\]\s*\?\?\s*\'\'=/', '$$$1[$i]=', $content);
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]\s*\?\?\s*\'\'/', '$$$1[$$2] ?? \'\'', $content);
    
    // Fix double dollar signs that shouldn't be there
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\?\?\s*\'\'/', '$$$1 ?? \'\'', $content);
    
    // Fix broken echo statements
    $content = preg_replace('/<\?phpecho\(/', '<?php echo(', $content);
    
    // Fix malformed for loops
    $content = preg_replace('/for\s*\([^)]+\):\s*\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$([a-zA-Z_][a-zA-Z0-9_]*)\]\s*\?\?\s*\'\'=([^;]+);\s*endfor;/', 'for ($i=0; $i<$in; $i++): $$$1[$i]=$3; endfor;', $content);
    
    // Fix specific problematic patterns
    $content = str_replace('$$s[$i] ?? \'\'=$s[$i-1]+$$n[$i] ?? \'\';', '$s[$i]=$s[$i-1]+$n[$i];', $content);
    $content = str_replace('$$n[$i] ?? \'\'', '$n[$i] ?? \'\'', $content);
    $content = str_replace('$$s[$i] ?? \'\'', '$s[$i] ?? \'\'', $content);
    
    // Fix array access patterns
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/', '$$$1[', $content);
    
    // Fix echo in value attributes
    $content = preg_replace('/value="<\?php echo\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\]\s*\?\?\s*\'\'\);\?>"/', 'value="<?php echo($$$1[$i] ?? \'\');?>"', $content);
    
    // Fix URL encoding issues
    $content = preg_replace('/echo\'([^\']*)\%5B\%5D=\',\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\]\s*\?\?\s*\'\',\'&\';/', 'echo\'$1%5B%5D=\',$$$2[$i],\'&\';', $content);
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Fixed syntax errors in: $filePath\n";
        return true;
    }
    
    echo "No syntax fixes needed: $filePath\n";
    return false;
}

// Get all PHP files that failed syntax check
$failedFiles = [
    'statisticsonweb.tf.czu.cz/Eanova.php',
    'statisticsonweb.tf.czu.cz/anova.php',
    'statisticsonweb.tf.czu.cz/Eanovadvoj.php',
    'statisticsonweb.tf.czu.cz/anovadvoj.php',
    'statisticsonweb.tf.czu.cz/Eanovadvojinter.php',
    'statisticsonweb.tf.czu.cz/anovadvojinter.php',
    'statisticsonweb.tf.czu.cz/Eanovadvou.php',
    'statisticsonweb.tf.czu.cz/Efriedm.php',
    'statisticsonweb.tf.czu.cz/friedm.php',
    'statisticsonweb.tf.czu.cz/Ekontin.php',
    'statisticsonweb.tf.czu.cz/kontin.php',
    'statisticsonweb.tf.czu.cz/Ekruskalwal.php',
    'statisticsonweb.tf.czu.cz/kruskalwal.php',
    'statisticsonweb.tf.czu.cz/Elinreg.php',
    'statisticsonweb.tf.czu.cz/linreg.php',
    'statisticsonweb.tf.czu.cz/Eparovy.php',
    'statisticsonweb.tf.czu.cz/parovy.php',
    'statisticsonweb.tf.czu.cz/Espearman.php',
    'statisticsonweb.tf.czu.cz/spearman.php',
    'statisticsonweb.tf.czu.cz/Ewildvouv.php',
    'statisticsonweb.tf.czu.cz/wildvouv.php',
    'statisticsonweb.tf.czu.cz/Ewiljednov.php',
    'statisticsonweb.tf.czu.cz/wiljednov.php',
    'statisticsonweb.tf.czu.cz/jednovjedn1.php'
];

echo "Fixing syntax errors in " . count($failedFiles) . " files...\n\n";

$fixedCount = 0;
foreach ($failedFiles as $file) {
    if (file_exists($file) && fixSyntaxErrors($file)) {
        $fixedCount++;
    }
}

echo "\nCompleted syntax fixes! Fixed $fixedCount files.\n";
?>
