<?php
/**
 * Configuration file for the Statistics Web Application
 */

// Prevent direct access
if (!defined('STATS_APP')) {
    die('Direct access not allowed');
}

// Application settings
define('APP_NAME', 'Statistical Hypothesis Testing');
define('APP_VERSION', '2.0.0');
define('APP_AUTHOR', 'Františ<PERSON>');

// Security settings
define('MAX_INPUT_SIZE', 30);
define('MIN_INPUT_SIZE', 2);
define('MAX_ARRAY_SIZE', 100);

// Supported locales
define('SUPPORTED_LOCALES', ['cs', 'en']);
define('DEFAULT_LOCALE', 'cs');

// File paths
define('DATA_DIR', __DIR__ . '/../data/');
define('LOGS_DIR', __DIR__ . '/../logs/');

// Statistical test limits
define('STAT_LIMITS', [
    'sample_size_min' => 2,
    'sample_size_max' => 30,
    'anova_classes_min' => 3,
    'anova_classes_max' => 10,
    'wilcoxon_min' => 6,
    'correlation_min' => 4
]);

// Error messages
define('ERROR_MESSAGES', [
    'cs' => [
        'invalid_input' => 'Neplatný vstup',
        'out_of_range' => 'Hodnota je mimo povolený rozsah',
        'invalid_sample_size' => 'Neplatná velikost vzorku (zadejte číslo od %d do %d)',
        'calculation_error' => 'Chyba při výpočtu',
        'file_error' => 'Chyba při čtení statistických tabulek',
        'csrf_error' => 'Bezpečnostní chyba - obnovte stránku'
    ],
    'en' => [
        'invalid_input' => 'Invalid input',
        'out_of_range' => 'Value is out of allowed range',
        'invalid_sample_size' => 'Invalid sample size (enter number from %d to %d)',
        'calculation_error' => 'Calculation error',
        'file_error' => 'Error reading statistical tables',
        'csrf_error' => 'Security error - please refresh the page'
    ]
]);

// Test descriptions
define('TEST_DESCRIPTIONS', [
    'cs' => [
        'jednov' => 'Jednovýběrový t-test (Studentův) oboustranný',
        'jednovjedn' => 'Jednovýběrový t-test (Studentův) jednostranný',
        'jednovS' => 'Jednovýběrový test pro rozptyl',
        'wiljednov' => 'Jednovýběrový Wilcoxonův test',
        'dvouv' => 'Dvouvýběrový t-test (Studentův)',
        'parovy' => 'Párový t-test',
        'dvouvF' => 'Dvouvýběrový F-test (Fischerův)',
        'wildvouv' => 'Dvouvýběrový Wilcoxonův test (Mannův - Whitneyův test)',
        'anova' => 'Jednoduché třídění (analýza rozptylu ANOVA)',
        'kruskalwal' => 'Kruskalův - Wallisův test',
        'pears' => 'Test Pearsonova korelačního koeficientu',
        'spearman' => 'Test Spearmanova korelačního koeficientu',
        'kontin' => 'Test nezávislosti v kontingenční tabulce',
        'linreg' => 'Lineární regrese'
    ],
    'en' => [
        'jednov' => 'One-sample t-test (Student) two-tailed',
        'jednovjedn' => 'One-sample t-test (Student) one-tailed',
        'jednovS' => 'One-sample test for variance',
        'wiljednov' => 'One-sample Wilcoxon signed-rank test',
        'dvouv' => 'Two-sample t-test (Student)',
        'parovy' => 'Paired t-test',
        'dvouvF' => 'Two-sample F-test (Fisher)',
        'wildvouv' => 'Two-sample Wilcoxon test (Mann-Whitney test)',
        'anova' => 'One-way ANOVA (Analysis of Variance)',
        'kruskalwal' => 'Kruskal-Wallis test',
        'pears' => 'Pearson correlation coefficient test',
        'spearman' => 'Spearman rank correlation coefficient test',
        'kontin' => 'Contingency table chi-square test',
        'linreg' => 'Linear regression'
    ]
]);

/**
 * Get localized message
 */
function getMessage($key, $locale = null) {
    if ($locale === null) {
        $locale = $_SESSION['locale'] ?? DEFAULT_LOCALE;
    }
    
    if (!in_array($locale, SUPPORTED_LOCALES)) {
        $locale = DEFAULT_LOCALE;
    }
    
    return ERROR_MESSAGES[$locale][$key] ?? $key;
}

/**
 * Get test description
 */
function getTestDescription($test, $locale = null) {
    if ($locale === null) {
        $locale = $_SESSION['locale'] ?? DEFAULT_LOCALE;
    }
    
    if (!in_array($locale, SUPPORTED_LOCALES)) {
        $locale = DEFAULT_LOCALE;
    }
    
    return TEST_DESCRIPTIONS[$locale][$test] ?? $test;
}

/**
 * Get current locale
 */
function getCurrentLocale() {
    return $_SESSION['locale'] ?? DEFAULT_LOCALE;
}

/**
 * Set application locale
 */
function setAppLocale($locale) {
    if (in_array($locale, SUPPORTED_LOCALES)) {
        $_SESSION['locale'] = $locale;
    }
}
?>
