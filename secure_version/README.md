# Secure Statistics Web Application

This is a security-hardened version of the Czech/English statistical hypothesis testing web application.

## Directory Structure

```
secure_version/
├── public/                 # Web-accessible files
│   ├── index.php          # Main entry point
│   ├── celekTEST.php      # Czech test menu
│   ├── EcelekTEST.php     # English test menu
│   ├── tests/             # Statistical test files
│   ├── assets/            # CSS, JS, images
│   └── .htaccess          # Apache security rules
├── includes/              # PHP includes (not web-accessible)
│   ├── config.php         # Configuration
│   ├── security.php       # Security functions
│   ├── functions.php      # Statistical functions
│   └── validation.php     # Input validation
├── data/                  # Statistical lookup tables
│   ├── stud1.txt
│   ├── stud2.txt
│   ├── chi1.txt
│   └── ...
└── logs/                  # Application logs
```

## Security Features Implemented

1. **Input Validation & Sanitization**
   - All user inputs are validated and sanitized
   - Type checking for numeric inputs
   - Range validation for statistical parameters

2. **XSS Protection**
   - All output is properly escaped
   - HTML entities encoded
   - Content Security Policy headers

3. **CSRF Protection**
   - CSRF tokens for all forms
   - Session-based token validation

4. **File Security**
   - Data files moved outside web root
   - Bounds checking for file operations
   - No direct file access from user input

5. **Error Handling**
   - Secure error messages
   - Logging of security events
   - No information disclosure

## Installation

1. Copy files to web server
2. Ensure `includes/`, `data/`, and `logs/` are NOT web-accessible
3. Configure web server to serve from `public/` directory only
4. Set proper file permissions

## PHP Requirements

- PHP 8.3+
- Session support
- Filter extension (usually included)

## Security Notes

- Never expose the `includes/`, `data/`, or `logs/` directories to web access
- Regularly update PHP and web server
- Monitor logs for suspicious activity
- Consider adding rate limiting for production use
