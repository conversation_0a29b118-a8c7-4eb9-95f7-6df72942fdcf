<?php
/**
 * Main entry point for the Statistics Web Application
 */

define('STATS_APP', true);

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';

// Initialize security
Security::init();

// Handle locale switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LOCALES)) {
    setAppLocale($_GET['lang']);
    // Redirect to remove lang parameter from URL
    header('Location: index.php');
    exit;
}

$locale = getCurrentLocale();
$isEnglish = ($locale === 'en');

?>
<!DOCTYPE html>
<html lang="<?php echo Security::escape($locale); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $isEnglish ? 'Statistical Tests' : 'Statistické testy'; ?></title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="language-switcher">
                <a href="?lang=cs" class="<?php echo !$isEnglish ? 'active' : ''; ?>">
                    <img src="assets/images/cz.png" width="30" height="20" alt="Česky"> Česky
                </a>
                <a href="?lang=en" class="<?php echo $isEnglish ? 'active' : ''; ?>">
                    <img src="assets/images/gb.png" width="30" height="20" alt="English"> English
                </a>
            </div>
        </header>

        <main>
            <div class="welcome-section">
                <?php if ($isEnglish): ?>
                    <h1>HYPOTHESIS TESTING</h1>
                    <p class="subtitle">Statistical Tests for Data Analysis</p>
                    <div class="action-buttons">
                        <a href="en/EcelekTEST.php" class="btn btn-primary">
                            <img src="assets/images/gb.png" width="40" height="27" alt="English">
                            Start Testing (English)
                        </a>
                    </div>
                <?php else: ?>
                    <h1>TESTOVÁNÍ HYPOTÉZ</h1>
                    <p class="subtitle">Statistické testy pro analýzu dat</p>
                    <div class="action-buttons">
                        <a href="celekTEST.php" class="btn btn-primary">
                            <img src="assets/images/cz.png" width="40" height="27" alt="Česky">
                            Začít testování (Česky)
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="features">
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3><?php echo $isEnglish ? 'One-sample Tests' : 'Jednovýběrové testy'; ?></h3>
                        <ul>
                            <li><?php echo $isEnglish ? 't-test (Student)' : 't-test (Studentův)'; ?></li>
                            <li><?php echo $isEnglish ? 'Variance test' : 'Test pro rozptyl'; ?></li>
                            <li><?php echo $isEnglish ? 'Wilcoxon test' : 'Wilcoxonův test'; ?></li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3><?php echo $isEnglish ? 'Two-sample Tests' : 'Dvouvýběrové testy'; ?></h3>
                        <ul>
                            <li><?php echo $isEnglish ? 't-test (Student)' : 't-test (Studentův)'; ?></li>
                            <li><?php echo $isEnglish ? 'F-test (Fisher)' : 'F-test (Fischerův)'; ?></li>
                            <li><?php echo $isEnglish ? 'Mann-Whitney test' : 'Mannův-Whitneyův test'; ?></li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3><?php echo $isEnglish ? 'ANOVA & Correlation' : 'ANOVA a korelace'; ?></h3>
                        <ul>
                            <li><?php echo $isEnglish ? 'Analysis of Variance' : 'Analýza rozptylu'; ?></li>
                            <li><?php echo $isEnglish ? 'Correlation tests' : 'Korelační testy'; ?></li>
                            <li><?php echo $isEnglish ? 'Linear regression' : 'Lineární regrese'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h2><?php echo $isEnglish ? 'Instructions' : 'Pokyny'; ?></h2>
                <div class="instruction-box">
                    <?php if ($isEnglish): ?>
                        <p><strong>Important:</strong> Use decimal point (not comma) for decimal numbers.</p>
                        <p>Navigate between form fields using the Tab key.</p>
                        <p>All statistical tests use significance level α = 0.05.</p>
                    <?php else: ?>
                        <p><strong>Důležité:</strong> Je nutné užívat desetinnou tečku (ne čárku) pro desetinná čísla.</p>
                        <p>Přechod mezi položkami pomocí klávesy Tab.</p>
                        <p>Všechny statistické testy používají hladinu významnosti α = 0,05.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; <?php echo Security::escape(APP_AUTHOR); ?></p>
                <p>
                    <?php if ($isEnglish): ?>
                        Czech University of Life Sciences in Prague, Charles University in Prague, Faculty of Education, Czech Republic
                    <?php else: ?>
                        Česká zemědělská univerzita v Praze, Karlova univerzita v Praze, Pedagogická fakulta, Česká republika
                    <?php endif; ?>
                </p>
                <p class="version">Version <?php echo Security::escape(APP_VERSION); ?></p>
            </div>
        </footer>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>
