<?php
/**
 * English One-sample t-test (Student) two-tailed
 * Secure version with input validation and XSS protection
 */

define('STATS_APP', true);

// Include required files
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/functions.php';

// Initialize security and set English locale
Security::init();
setAppLocale('en');

// Get and validate input parameters
$a = Security::getParam('a', 'int', 0);
$n = Security::getParam('n', 'int', null);
$x = Security::getParam('x', 'array', []);
$aa = Security::getParam('aa', 'float', null);
$csrf_token = Security::getParam('csrf_token', 'string', '');

// Validate CSRF token for form submissions
if ($_SERVER['REQUEST_METHOD'] === 'GET' && $a > 0) {
    if (!Security::validateCsrfToken($csrf_token)) {
        Security::logSecurityEvent("CSRF token validation failed for Ejednov.php");
        $error_message = getMessage('csrf_error', 'en');
        $a = 0; // Reset to initial state
    }
}

// Error handling
$error_message = '';
$calculation_results = null;

try {
    switch ($a) {
        case 1:
            // Validate sample size
            if ($n === null || $n < STAT_LIMITS['sample_size_min'] || $n > STAT_LIMITS['sample_size_max']) {
                $error_message = sprintf(getMessage('invalid_sample_size', 'en'), 
                    STAT_LIMITS['sample_size_min'], STAT_LIMITS['sample_size_max']);
                $a = 0;
            }
            break;
            
        case 2:
            // Validate all inputs for calculation
            if ($n === null || $n < STAT_LIMITS['sample_size_min'] || $n > STAT_LIMITS['sample_size_max']) {
                $error_message = sprintf(getMessage('invalid_sample_size', 'en'), 
                    STAT_LIMITS['sample_size_min'], STAT_LIMITS['sample_size_max']);
                $a = 1;
            } elseif (count($x) !== $n) {
                $error_message = "Number of data points must match sample size";
                $a = 1;
            } elseif ($aa === null) {
                $error_message = "Hypothesized mean value is required";
                $a = 1;
            } else {
                // Perform calculation
                try {
                    $mean = StatFunctions::mean($x);
                    $variance = StatFunctions::smodch($x);
                    $std_error = sqrt($variance / $n);
                    $t_stat = ($mean - $aa) / $std_error;
                    $df = $n - 1;
                    $critical_value = StatFunctions::invt1($df);
                    
                    $calculation_results = [
                        'mean' => StatFunctions::zaokr($mean, 4),
                        'variance' => StatFunctions::zaokr($variance, 4),
                        't_statistic' => StatFunctions::zaokr($t_stat, 3),
                        'degrees_freedom' => $df,
                        'critical_value' => $critical_value,
                        'reject_null' => abs($t_stat) > $critical_value,
                        'sample_size' => $n,
                        'hypothesized_mean' => $aa
                    ];
                } catch (Exception $e) {
                    $error_message = getMessage('calculation_error', 'en');
                    Security::logSecurityEvent("Calculation error in Ejednov.php: " . $e->getMessage());
                    $a = 1;
                }
            }
            break;
    }
} catch (Exception $e) {
    $error_message = getMessage('calculation_error', 'en');
    Security::logSecurityEvent("Error in Ejednov.php: " . $e->getMessage());
    $a = 0;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>One-sample t-test (Student) two-tailed</title>
    <link rel="stylesheet" href="../assets/style.css">
    <script>
        function openFormula() {
            window.open('../assets/images/jednov.png', 'formula', 'width=700,height=400');
        }
        function closeFormula() {
            // Formula window will close automatically when this window closes
        }
    </script>
</head>
<body onunload="closeFormula()">
    <div class="container">
        <header>
            <div class="breadcrumb">
                <a href="EcelekTEST.php">← Back to Test Menu</a>
                <span class="separator">|</span>
                <a href="#" onclick="openFormula()">View Formulas</a>
            </div>
            <h1>One-sample t-test (Student) two-tailed</h1>
        </header>

        <main>
            <?php if ($error_message): ?>
                <div class="error-message">
                    <strong>Error:</strong> <?php echo Security::escape($error_message); ?>
                </div>
            <?php endif; ?>

            <?php switch ($a): case 0: ?>
                <div class="test-form">
                    <h2>Step 1: Enter Sample Size</h2>
                    <form method="get" class="input-form">
                        <div class="form-group">
                            <label for="n">Sample size (n):</label>
                            <input type="number" 
                                   id="n" 
                                   name="n" 
                                   min="<?php echo STAT_LIMITS['sample_size_min']; ?>" 
                                   max="<?php echo STAT_LIMITS['sample_size_max']; ?>" 
                                   value="<?php echo Security::escape($n); ?>" 
                                   required>
                            <span class="help-text">Enter integer from <?php echo STAT_LIMITS['sample_size_min']; ?> to <?php echo STAT_LIMITS['sample_size_max']; ?></span>
                        </div>
                        
                        <div class="form-actions">
                            <input type="hidden" name="a" value="1">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::getCsrfToken(); ?>">
                            <button type="submit" class="btn btn-primary">Continue</button>
                        </div>
                    </form>
                </div>
                
            <?php break; case 1: ?>
                <div class="test-form">
                    <h2>Step 2: Enter Data and Hypothesis</h2>
                    <p><strong>Test level:</strong> α = 0.05</p>
                    
                    <form method="get" class="input-form">
                        <div class="form-group">
                            <label for="n">Sample size (n):</label>
                            <input type="number" 
                                   id="n" 
                                   name="n" 
                                   min="<?php echo STAT_LIMITS['sample_size_min']; ?>" 
                                   max="<?php echo STAT_LIMITS['sample_size_max']; ?>" 
                                   value="<?php echo Security::escape($n); ?>" 
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label>Random sample from N(μ, σ²) - X₁, ..., X<?php echo Security::escape($n); ?>:</label>
                            <div class="data-inputs">
                                <?php for ($i = 0; $i < $n; $i++): ?>
                                    <input type="number" 
                                           step="any" 
                                           name="x[]" 
                                           value="<?php echo Security::escape($x[$i] ?? ''); ?>" 
                                           placeholder="X<?php echo $i + 1; ?>"
                                           required>
                                <?php endfor; ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="aa">Null hypothesis H₀: μ =</label>
                            <input type="number" 
                                   step="any" 
                                   id="aa" 
                                   name="aa" 
                                   value="<?php echo Security::escape($aa); ?>" 
                                   required>
                        </div>
                        
                        <div class="form-actions">
                            <input type="hidden" name="a" value="2">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::getCsrfToken(); ?>">
                            <button type="submit" class="btn btn-primary">Perform Test</button>
                        </div>
                    </form>
                </div>
                
            <?php break; case 2: ?>
                <?php if ($calculation_results): ?>
                    <div class="results">
                        <h2>Test Results</h2>
                        
                        <div class="results-summary">
                            <div class="result-item">
                                <strong>Sample mean (X̄):</strong> <?php echo Security::escape($calculation_results['mean']); ?>
                            </div>
                            <div class="result-item">
                                <strong>Sample variance (S²):</strong> <?php echo Security::escape($calculation_results['variance']); ?>
                            </div>
                            <div class="result-item">
                                <strong>t-statistic:</strong> <?php echo Security::escape($calculation_results['t_statistic']); ?>
                            </div>
                            <div class="result-item">
                                <strong>Degrees of freedom:</strong> <?php echo Security::escape($calculation_results['degrees_freedom']); ?>
                            </div>
                            <div class="result-item">
                                <strong>Critical value t₀.₀₂₅:</strong> ±<?php echo Security::escape($calculation_results['critical_value']); ?>
                            </div>
                        </div>
                        
                        <div class="conclusion <?php echo $calculation_results['reject_null'] ? 'reject' : 'accept'; ?>">
                            <?php if ($calculation_results['reject_null']): ?>
                                <h3>Conclusion: Reject H₀</h3>
                                <p>|t| > t₀.₀₂₅(<?php echo Security::escape($calculation_results['degrees_freedom']); ?>)</p>
                                <p>The sample mean significantly differs from the hypothesized value μ = <?php echo Security::escape($calculation_results['hypothesized_mean']); ?> at α = 0.05 level.</p>
                            <?php else: ?>
                                <h3>Conclusion: Do not reject H₀</h3>
                                <p>|t| ≤ t₀.₀₂₅(<?php echo Security::escape($calculation_results['degrees_freedom']); ?>)</p>
                                <p>The sample mean does not significantly differ from the hypothesized value μ = <?php echo Security::escape($calculation_results['hypothesized_mean']); ?> at α = 0.05 level.</p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="related-tests">
                            <h3>Related Tests</h3>
                            <div class="test-links">
                                <a href="Ejednovjedn.php?n=<?php echo $n; ?>&<?php 
                                    for ($i = 0; $i < $n; $i++) echo "x[]=" . urlencode($x[$i]) . "&"; 
                                    echo "aa=" . urlencode($aa) . "&a=1"; ?>" class="btn btn-secondary">
                                    One-sample t-test one-tailed
                                </a>
                                <a href="EjednovS.php?n=<?php echo $n; ?>&<?php 
                                    for ($i = 0; $i < $n; $i++) echo "x[]=" . urlencode($x[$i]) . "&"; 
                                    echo "aa=" . urlencode($aa) . "&a=1"; ?>" class="btn btn-secondary">
                                    One-sample test for variance
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="form-actions">
                    <form method="get">
                        <input type="hidden" name="a" value="0">
                        <button type="submit" class="btn btn-outline">New Entry</button>
                    </form>
                </div>
                
            <?php endswitch; ?>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; <?php echo Security::escape(APP_AUTHOR); ?></p>
            </div>
        </footer>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>
