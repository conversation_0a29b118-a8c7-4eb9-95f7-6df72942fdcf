<?php
/**
 * <PERSON>ript to fix undefined variable issues for PHP 8.3 compatibility
 */

function fixVariableInitialization($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Common variable patterns that need null coalescing
    $patterns = [
        '/\$a = \$_GET\[\'a\'\];/' => '$a = $_GET[\'a\'] ?? 0;',
        '/\$n = \$_GET\[\'n\'\];/' => '$n = $_GET[\'n\'] ?? null;',
        '/\$x = \$_GET\[\'x\'\];/' => '$x = $_GET[\'x\'] ?? [];',
        '/\$y = \$_GET\[\'y\'\];/' => '$y = $_GET[\'y\'] ?? [];',
        '/\$aa = \$_GET\[\'aa\'\];/' => '$aa = $_GET[\'aa\'] ?? null;',
        '/\$m = \$_GET\[\'m\'\];/' => '$m = $_GET[\'m\'] ?? null;',
        '/\$c = \$_GET\[\'c\'\];/' => '$c = $_GET[\'c\'] ?? null;',
        '/\$in = \$_GET\[\'in\'\];/' => '$in = $_GET[\'in\'] ?? null;',
        '/\$ir = \$_GET\[\'ir\'\];/' => '$ir = $_GET[\'ir\'] ?? null;',
        '/\$is = \$_GET\[\'is\'\];/' => '$is = $_GET[\'is\'] ?? null;',
        '/\$ip = \$_GET\[\'ip\'\];/' => '$ip = $_GET[\'ip\'] ?? null;',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // Fix array access with null coalescing
    $content = preg_replace('/value="<\?php echo\(\$([a-zA-Z_][a-zA-Z0-9_]*)\);\?>"/', 'value="<?php echo($$$1 ?? \'\');?>"', $content);
    $content = preg_replace('/value="<\?php echo\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\]\);\?>"/', 'value="<?php echo($$$1[$i] ?? \'\');?>"', $content);
    
    // Fix array access in loops
    $content = preg_replace('/\$([a-zA-Z_][a-zA-Z0-9_]*)\[\$i\](?!\s*\?\?)/', '$$$1[$i] ?? \'\'', $content);
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Fixed variables in: $filePath\n";
        return true;
    }
    
    echo "No variable changes needed: $filePath\n";
    return false;
}

// Get all PHP files
$phpFiles = glob('statisticsonweb.tf.czu.cz/*.php');

echo "Found " . count($phpFiles) . " PHP files\n";
echo "Starting variable fixes...\n\n";

$fixedCount = 0;
foreach ($phpFiles as $file) {
    if (fixVariableInitialization($file)) {
        $fixedCount++;
    }
}

echo "\nCompleted variable fixes! Fixed $fixedCount files.\n";
?>
