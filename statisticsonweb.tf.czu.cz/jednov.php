<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jednovýběrový t-test</title>
<?php

$a = $_GET['a'] ?? 0;
$n = $_GET['n'] ?? null;
$x = $_GET['x'] ?? [];
$aa = $_GET['aa'] ?? null;

function mean($xvar)
  {$s =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s =$s + $xvar[$k]; endfor;
  $s=$s/$mez;
  return $s;} 

function sctv($xvar,$me)
  {$sc =0; $mez =count($xvar);
  for ($k =0;$k <$mez; $k++): $sc =$sc + pow(($xvar[$k]-$me),2); endfor;
  return $sc;} 

function smodch($xvar)
  {$s =0;$sc =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s =$s + $xvar[$k]; $sc =$sc + pow($xvar[$k],2); endfor;
  $sp=($sc-$s*$s/$mez)/($mez-1);
  return $sp;}

function invt1($sv)
  {$stud=fopen("stud2.txt","r");
  $stav=($sv-1)*7;
  fseek($stud,$stav);
  $inv=fread($stud,5);
  fclose($stud);
  return $inv;}

function zaokr($cislo,$des)
  {$moc=pow(10,$des);
  $vysl=round($cislo*$moc)/$moc;
  return $vysl;} 

?>


<script>
function otev(){vzor = window.open("jednov.png","vzor","width=700, height=400");}
function zav(){if (! vzor.closed) vzor.close();}
</script>
</head>

<body bgcolor="navajowhite" link="saddlebrown" alink="chocolate" vlink="darkgoldenrod" onunload="zav()">

<table><tr align="center">
<td><br><h2>Jednovýběrový t-test (Studentův) oboustranný:</h2></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="celekTEST.php">seznam testů</a></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a onmouseover="otev()">vzorce</a></td>
<td><a onmouseover="zav()">(zavřít)</a></td>
</tr></table>

<?phpphp switch($a):

case 0: ?>

<form method="get"> hladina testu &nbsp;&nbsp; &alpha; = 0,05
  <br> rozsah &nbsp;&nbsp; n:  &nbsp;
  <input type="number" size="1" name="n" value="<?phpphp echo($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="ano"> &nbsp;(zadejte číslo od 2 do 30)
  <input type="hidden" name="a" value="1">
</form>

<?phpphp break;

case 1: 

if($n<2||$n>30||!(round($n)==$n)): ?>
<form method="get"> hladina testu &nbsp;&nbsp; &alpha; = 0,05
  <br> rozsah &nbsp;&nbsp; n:  &nbsp;
  <input type="number" size="1" name="n" value="<?phpphp echo($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="ano"> &nbsp; (zadejte číslo od 2 do 30)
  <input type="hidden" name="a" value="1">
</form>
<?phpphp echo("<font color=\"red\">nezadali jste celé číslo mezi 2 a 30, opravte</font>"); break;

else: ?>
<form method="get"> hladina testu &nbsp;&nbsp; &alpha; = 0,05
  <br> rozsah &nbsp;&nbsp; n:  &nbsp;
  <input type="number" size="1" name="n" value="<?phpphp echo($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="ano"> &nbsp; (zadejte číslo od 2 do 30)
  <br> náhodný výběr z N(&mu;, &sigma;&sup2;) &nbsp;&nbsp; X<sub>1</sub>,...,X<sub><?phpphp echo($n);?></sub>: &nbsp;
  <?phpphp for ($i =0; $i <$n; $i++): ?>
   <input type="number" step="any" name="x[]" size="1" value="<?phpphp echo($x[$i] ?? '');?>">
  <?phpphp endfor;?>
  <br> nulová hypotéza &nbsp;&nbsp; H<sub>0</sub> : &mu; = <input type="number" step="any" size="1" name="aa" value="<?phpphp echo($aa);?>"> <br>
  <input type="submit" value="proveďte test">
  <input type="hidden" name="a" value="2">
</form>
<?phpphp break;
endif;

case 2: 

if($n<2||$n>30||!(round($n)==$n)): ?>
<form method="get"> hladina testu &nbsp;&nbsp; &alpha; = 0,05 
  <br> rozsah &nbsp;&nbsp; n:  &nbsp;
  <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="ano"> &nbsp; (zadejte číslo od 2 do 30)
  <input type=hidden name=a value="1">
</form>
<?phpphp echo("<font color=red>nezadali jste celé číslo mezi 2 a 30, opravte</font>"); break;

else: ?>
<form method="get"> hladina testu &nbsp;&nbsp; &alpha; = 0,05 
  <br> rozsah &nbsp;&nbsp; n:  &nbsp; 
  <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="ano"> &nbsp; (zadejte číslo od 2 do 30)
  <br> náhodný výběr z N(&mu;, &sigma;&sup2;) &nbsp;&nbsp; X <sub>1</sub>,...,X<sub><?phpphp echo($n);?></sub> : 
  <?phpphp for ($i =0; $i <$n; $i++): ?>
   <input type=double name="x[]" size="1"  value="<?phpecho($$x[$i] ?? '');?>">
  <?phpphp endfor;?> 
  <br> nulová hypotéza &nbsp;&nbsp; H<sub>0</sub> : &mu; = <input type=double size=1 name="aa" value="<?phpecho($aa);?>"><br>
  <input type="submit" value="proveďte test">
  <input type=hidden name=a value="2">
</form>
<?phpphp
  $m=mean($x); $zm=zaokr($m,4);
  $so=smodch($x); $zso=zaokr($so,4) ?> 
  <br>
   <span style="text-decoration: overline">X</span> = <?phpphp echo($zm);?> &nbsp;&nbsp;&nbsp;&nbsp; 
    S<sub>x</sub>&sup2; = <?phpphp echo($zso); ?>
<?phpphp 
  if ($so==0):?> 
    <br> <font color=red> rozptyl se rovná 0, tento test nelze užít </font>
  <?phpphp 
  else:
    $t=($m-$aa)/sqrt($so)*sqrt($n);
    $zt=zaokr($t,3); 
    $sv=count($x)-1;
    $inv=invt1($sv);
    $konf1=$m-sqrt($so)*$inv/sqrt($n);
    $konf2=$m+sqrt($so)*$inv/sqrt($n);
    $zkonf1=zaokr($konf1,4);
    $zkonf2=zaokr($konf2,4);
  ?>
    <br> 
    T = <?phpphp echo($zt);?> &nbsp;&nbsp;&nbsp;&nbsp; 
    t<sub><?phpphp echo($sv)?></sub> <?phpphp echo("(0.975) = ".$inv);?>
    <br>
  <?phpphp 
    if(Max($t,-$t)>=$inv): ?> 
      |T| &ge; t<sub><?phpphp echo($sv)?></sub> <?phpphp echo("(0.975)");?> 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; hypotézu &nbsp;&nbsp; H<sub>0</sub> : &mu; = <?phpphp echo($aa);?> &nbsp;&nbsp;zamítneme
    <?phpphp 
    else: ?> 
      |T| < t<sub><?phpphp echo($sv)?></sub> <?phpphp echo("(0.975)");?> 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; hypotézu &nbsp;&nbsp; H<sub>0</sub> : &mu; = <?phpphp echo($aa);?> &nbsp;&nbsp;nezamítneme
    <?phpphp 
    endif; ?>
    <br><br> konfidenční interval (95%):&nbsp;&nbsp;&nbsp;&nbsp; 
    <?phpphp echo("( ".$zkonf1." ; ".$zkonf2." )");
  endif; ?> 

<br><br>
<?phpphp
echo'<a href="jednovjedn.php?n=',$n,'&';
for ($i =0; $i <$n; $i++): 
echo'x%5B%5D=',$$x[$i] ?? '','&';
endfor;
echo'aa=',$aa,'& a=1"> jednovýběrový t-test jednnostranný </a>';
?>
&nbsp;&nbsp;
<?phpphp
echo'<a href="jednovS.php?n=',$n,'&';
for ($i =0; $i <$n; $i++): 
echo'x%5B%5D=',$$x[$i] ?? '','&';
endfor;
echo'aa=',$aa,'& a=1"> jednovýběrový test pro rozptyl </a>';
?>
&nbsp;&nbsp;
<?phpphp
echo'<a href="wiljednov.php?n=',$n,'&';
for ($i =0; $i <$n; $i++): 
echo'x%5B%5D=',$$x[$i] ?? '','&';
endfor;
echo'aa=',$aa,'& a=1"> jednovýběrový Wilcoxonův test </a>';
?>

<form>
  <input type="submit" value="nové zadání">
  <input type=hidden name=a value="0">
</form>

<?php
endif;
default:
endswitch;?>
</body>
</html>



 