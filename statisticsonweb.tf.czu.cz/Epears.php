<html>
<head>\n    <meta charset="UTF-8">
<?phpphp 

$a = $_GET['a'] ?? 0;
$n = $_GET['n'] ?? null;
$x = $_GET['x'] ?? [];
$y = $_GET['y'] ?? [];

function mean($xvar)
  {$s =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s =$s + $xvar[$k]; endfor;
  $s=$s/$mez;
  return $s;} 

function sctv($xvar,$me)
  {$sc =0; $mez =count($xvar);
  for ($k =0;$k <$mez; $k++): $sc =$sc + pow(($xvar[$k]-$me),2); endfor;
  return $sc;} 

function smodch($xvar)
  {$s =0;$sc =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s =$s + $xvar[$k]; $sc =$sc + pow($xvar[$k],2); endfor;
  $sp=($sc-$s*$s/$mez)/($mez-1);
  return $sp;}

function cov($xvar,$yvar)
  {$s1 =0; $s2=0; $sn =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s1 =$s1 + $xvar[$k];$s2 =$s2 + $yvar[$k]; 
        $sn =$sn + $xvar[$k]*$yvar[$k]; endfor;
  $co=($sn-$s1*$s2/$mez)/($mez-1);
  return $co;}

function invt1($sv)
  {$stud=fopen("stud2.txt","r");
  $stav=($sv-1)*7;
  fseek($stud,$stav);
  $inv=fread($stud,5);
  fclose($stud);
  return $inv;}  

function zaokr($cislo,$des)
  {$moc=pow(10,$des);
  $vysl=round($cislo*$moc)/$moc;
  return $vysl;} 

?>
<script>
function otev(){vzor = window.open("Ekorel.png","vzor","width=800, height=350");}
function zav(){if (! vzor.closed) vzor.close();}
</script>
</head>

<body bgcolor=navajowhite link=saddlebrown alink=chocolate vlink=darkgoldenrod onunload="zav()">

<table><tr align="center">
<td><br><h2>Pearson correlation coefficient:</h2></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="EcelekTEST.php">list of tests</a></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a OnMouseOver=otev()>formulas</a></td>
<td><a OnMouseOver=zav()>(close)</a></td>
</tr></table>

<?phpphp switch($a):

case 0: ?>

<form method="get"> test level &nbsp;&nbsp; &alpha; = 0,05 
  <br> range &nbsp;&nbsp; n: &nbsp; 
  <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
  <input type="submit" value="yes">  &nbsp; (enter an integer from 3 to 30)
  <input type=hidden name=a value="1">
</form>

<?phpphp break;

case 1: 

if($n<3||$n>30||!(round($n)==$n)): ?>

  <form method="get"> test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> range &nbsp;&nbsp; n:  &nbsp;  
    <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
    <input type="submit" value="yes"> &nbsp; (enter an integer from 3 to 30)
    <input type=hidden name=a value="1">
  </form>

  <?phpphp echo("<font color=red> you did not enter an integer between 3 and 30, correct </font>");  break;

else: ?>

  <form method="get"> test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> range &nbsp;&nbsp; n:  &nbsp;  
    <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
    <input type="submit" value="yes">  &nbsp; (enter an integer from 3 to 30)
    <br>  random sample from normal distribution  &nbsp;&nbsp; 
    (X<sub>1</sub>,Y<sub>1</sub>),...,(X<sub><?phpphp echo($n);?></sub>,Y<sub><?phpphp echo($n);?></sub>) 
    <br> X<sub>1</sub>,...,X<sub><?phpphp echo($n);?></sub> : &nbsp;&nbsp;&nbsp;&nbsp;
    <?phpphp for ($i =0; $i <$n; $i++): ?>
    <input type=double name="x[]" size="1" value="<?phpecho($$x[$i] ?? '');?>">
    <?phpphp endfor;?> 
    <br> Y<sub>1</sub>,...,Y<sub><?phpphp echo($n);?></sub>: &nbsp;&nbsp;&nbsp;&nbsp;
    <?phpphp for ($i =0; $i <$n; $i++): ?>
    <input type=double name="y[]" size="1" value="<?phpecho($$y[$i] ?? '');?>">
    <?phpphp endfor;?> 
    <br> null hypothesis &nbsp;&nbsp; H<sub>0</sub>: &rho; = 0 <br>
    <input type="submit" value="perform the test">
    <input type=hidden name=a value="2">
  </form>
 
  <?phpphp break;
endif;

case 2: 

if($n<3||$n>30||!(round($n)==$n)):  ?>

  <form method="get"> test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> range &nbsp;&nbsp; n:  &nbsp;  
    <input type=integer size=1 name="n" value="<?phpecho($n);?>"> &nbsp;&nbsp;&nbsp;&nbsp;
    <input type="submit" value="yes"> &nbsp; (enter an integer from 3 to 30)
    <input type=hidden name=a value="1">
  </form>

<?phpphp echo("<font color=red>you did not enter an integer between 3 and 30, correct</font>"); break;

else: ?>

  <form method="get"> test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> range &nbsp;&nbsp; n: &nbsp;  
    <input type=integer size=1 name="n" value="<?phpecho($n);?>">&nbsp;&nbsp;&nbsp;&nbsp;
    <input type="submit" value="yes"> &nbsp; (enter an integer from 3 to 30)
    <br> random sample from normal distribution &nbsp;&nbsp; (X<sub>1</sub>,Y<sub>1</sub>),...,(X<sub><?phpphp echo($n);?></sub>,Y<sub><?phpphp echo($n);?></sub>) 
    <br> X<sub>1</sub>,...,X<sub><?phpphp echo($n);?></sub>: &nbsp;&nbsp;&nbsp;&nbsp;
    <?phpphp for ($i =0; $i <$n; $i++): ?>
    <input type=double name="x[]" size="1" value="<?phpecho($$x[$i] ?? '');?>">
    <?phpphp endfor;?> 
    <br> Y<sub>1</sub>,...,Y<sub><?phpphp echo($n);?></sub>: &nbsp;&nbsp;&nbsp;&nbsp;
    <?phpphp for ($i =0; $i <$n; $i++): ?>
    <input type=double name="y[]" size="1" value="<?phpecho($$y[$i] ?? '');?>">
    <?phpphp endfor;?> 
    <br> null hypothesis &nbsp;&nbsp; H<sub>0</sub>:  &rho; = 0 <br>
    <input type="submit" value="perform the test">
    <input type=hidden name=a value="2">
  </form>

  <br><?phpphp
  $sv=$n-2;
  $m1=mean($x); $m2=mean($y); $zm1=zaokr($m1,4);$zm2=zaokr($m2,4);
  $so1=smodch($x);$so2=smodch($y);$zso1=zaokr($so1,4);$zso2=zaokr($so2,4);
  $sxy=cov($x,$y); $zsxy=zaokr($sxy,4);
  $inv=invt1($sv);  ?>
  <span style="text-decoration: overline">X</span> = <?phpphp echo($zm1);?> &nbsp;&nbsp;&nbsp;&nbsp; 
  <span style="text-decoration: overline">Y</span> = <?phpphp echo($zm2);?> &nbsp;&nbsp;&nbsp;&nbsp;
  S<sub>X</sub>&sup2; = <?phpphp echo($zso1);?> &nbsp;&nbsp;&nbsp;&nbsp;
  S<sub>Y</sub>&sup2; = <?phpphp echo($zso2);?> &nbsp;&nbsp;&nbsp;&nbsp;
  S<sub>XY</sub> = <?phpphp echo($zsxy);?>
  
  <?phpphp 
  if($so1*$so2==0):?> 
    <br><font color=red> at least one variance is equal to 0, this test cannot be used </font><?phpphp 
  else:
    $r=$sxy/sqrt($so1)/sqrt($so2);
    if($r>=0.9999||$r<=-0.9999):?>
      <br><font color=red> correlation is equal to 1 or -1, this test cannot be used </font><?phpphp   
    else:
      $t=$r/sqrt(1-$r*$r)*sqrt($sv);    
      $zr=zaokr($r,4);
      $zt=zaokr($t,3); ?> <br> 
      r = <?phpphp echo($zr);?>&nbsp;&nbsp;&nbsp;&nbsp;  
      T = <?phpphp echo($zt);?>&nbsp;&nbsp;&nbsp;&nbsp; 
      t<sub><?phpphp echo($sv)?></sub><?phpphp echo("(0.975) = ".$inv);?><br><?phpphp 
        if(Max($t,-$t)>=$inv):?> 
      |T| &ge; t<sub><?phpphp echo($sv)?></sub> <?phpphp echo("(0.975)");?> 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
      hypothesis &nbsp;&nbsp; H<sub>0</sub>: &rho; = 0 &nbsp;&nbsp; is rejected <?phpphp 
        else: ?> 
      |T| < t<sub><?phpphp echo($sv)?></sub> <?phpphp echo("(0.975)");?> 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
      hypothesis &nbsp;&nbsp; H<sub>0</sub>: &rho; = 0 &nbsp;&nbsp; is not rejected<?phpphp 
        endif;
    endif;
  endif;?>

<br><br>
<?phpphp
echo'<a href="Espearman.php?n=',$n,'&';
for ($i =0; $i <$n; $i++): 
echo'x%5B%5D=',$$x[$i] ?? '','&';
endfor;
for ($j =0; $j <$n; $j++): 
echo'y%5B%5D=',$y[$j],'&';
endfor;
echo 'a=1"> Spearman correlation coeficient </a>';
?>
&nbsp;&nbsp;
<?phpphp
echo'<a href="Elinreg.php?n=',$n,'&';
for ($i =0; $i <$n; $i++): 
echo'x%5B%5D=',$$x[$i] ?? '','&';
endfor;
for ($j =0; $j <$n; $j++): 
echo'y%5B%5D=',$y[$j],'&';
endfor;
echo 'a=1"> linear regression </a>';
?>



  <form>
    <input type="submit" value="new entry">
    <input type=hidden name=a value="0">
  </form>
 <?php
endif;
default:
endswitch;?>
</body>
</html>




