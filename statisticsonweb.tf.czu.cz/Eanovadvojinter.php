<html>
<head>\n    <meta charset="UTF-8">
<?phpphp

$a = $_GET['a'];
$ir = $_GET['ir'];
$is = $_GET['is'];
$ip = $_GET['ip'];
$x = $_GET['x'];

function sum($xvar)
  {$s =0; $mez =count($xvar);
  for ($k =0;$k <$mez;$k++): $s =$s + $xvar[$k]; endfor;
  return $s;} 

function sctv($xvar,$me)
  {$sc =0; $mez =count($xvar);
  for ($k =0;$k <$mez; $k++): $sc =$sc + pow(($xvar[$k]-$me),2); endfor;
  return $sc;} 


function invf($sv,$sw)
  {$fis=fopen("fis3.txt","r");
  $stav=($sw-1)*151+($sv-1)*5;
  fseek($fis,$stav);
  $inv=fread($fis,4);
  fclose($fis);
  return $inv;} 

function zaokr($cislo,$des)
  {$moc=pow(10,$des);
  $vysl=round($cislo*$moc)/$moc;
  return $vysl;} 
?>

<script>
function otev(){vzor = window.open("Eanovadvouinter.png","vzor","width=950, height=700");}
function zav(){if (! vzor.closed) vzor.close();}
</script>
</head>

<body bgcolor=navajowhite link=saddlebrown alink=chocolate vlink=darkgoldenrod onunload="zav()">

<table><tr align="center">
<td><br><h2>Two-way ANOVA and interactions:</h2></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="EcelekTEST.php">list of tests</a></td>
<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a OnMouseOver=otev()>formulas</a></td>
<td><a OnMouseOver=zav()>(close)</a></td>
</tr></table>

<?phpphp 
switch($a):

case 0: ?>
    <form method="get">  test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> number of categories A &nbsp;&nbsp; r: &nbsp; 
    <input type=integer size=1 name="ir" value="<?phpecho($ir);?>">  
    &nbsp;&nbsp;&nbsp;&nbsp; number of categories B &nbsp;&nbsp; s: &nbsp;
    <input type=integer size=1 name="is" value="<?phpecho($is);?>"> &nbsp; (enter integers from 2 to 10) ,
    &nbsp;&nbsp;&nbsp;&nbsp; range od categories &nbsp;&nbsp; p: &nbsp;
    <input type=integer size=1 name="ip" value="<?phpecho($ip);?>">      &nbsp; (enter an integer from 2 to 10)
    <input type="submit" value="yes">
    <input type=hidden name=a value="1">
  </form>
<?phpphp break;

case 1: 

if($ir<2||$is<2||$ip<2||$ir>10||$is>10||$ip>10||!(round($ir)==$ir)||!(round($is)==$is)||!(round($ip)==$ip)): ?>
    <form method="get">  test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> number of categories A &nbsp;&nbsp; r: &nbsp; 
    <input type=integer size=1 name="ir" value="<?phpecho($ir);?>">  
    &nbsp;&nbsp;&nbsp;&nbsp; number of categories B &nbsp;&nbsp; s: &nbsp;
    <input type=integer size=1 name="is" value="<?phpecho($is);?>"> &nbsp; (enter integers from 2 to 10) ,
    &nbsp;&nbsp;&nbsp;&nbsp;  range od categories &nbsp;&nbsp; p: &nbsp;
    <input type=integer size=1 name="ip" value="<?phpecho($ip);?>">      &nbsp; (enter an integer from 2 to 10)
    <input type="submit" value="yes">
    <input type=hidden name=a value="1">
  </form>

  <?phpphp echo("<font color=red> you did not enter required integers, correct</font>"); break;

else: ?>

    <form method="get">  test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> number of categories A &nbsp;&nbsp; r: &nbsp; 
    <input type=integer size=1 name="ir" value="<?phpecho($ir);?>">  
    &nbsp;&nbsp;&nbsp;&nbsp; number of categories B &nbsp;&nbsp; s: &nbsp;
    <input type=integer size=1 name="is" value="<?phpecho($is);?>"> &nbsp; (zadejte čísla od 2 do 10) ,
    &nbsp;&nbsp;&nbsp;&nbsp; range of categories &nbsp;&nbsp; p: &nbsp;
    <input type=integer size=1 name="ip" value="<?phpecho($ip);?>"> &nbsp; (zadejte číslo od 2 do 10)
    <input type="submit" value="yes">
  <br> random samples from &nbsp;
<table><tr>
N(&mu;<sub>1,1</sub>, &sigma;&sup2;) ,...,  N(&mu;<sub><?phpphp echo("1,".$is);?></sub>, &sigma;&sup2;)
</tr><tr>..............................................</tr>
<tr>
N(&mu;<sub><?phpphp echo($ir.",1");?></sub>, &sigma;&sup2;) ,...,  N(&mu;<sub><?phpphp echo($ir.",".$is);?></sub>, &sigma;&sup2;)
</tr></table>
&nbsp;&nbsp;&nbsp;
<table><tr>
 &mu;<sub>1,1</sub> = &mu; + &alpha;<sub>1</sub>+ &beta;<sub>1</sub>+ &gamma;<sub>1,1</sub>,..., 
                   &mu;<sub><?phpphp echo("1,".$is);?></sub>= &mu;+ &alpha;<sub>1</sub>+ &beta;<sub><?phpphp echo($is);?></sub>+ &gamma;<sub><?phpphp echo("1,".$is);?></sub>
</tr><tr> ...............................................................................</tr>
 &mu;<sub><?phpphp echo($ir.",1") ?></sub> = &mu; + &alpha;<sub><?phpphp echo($ir);?></sub>+ &beta;<sub>1</sub>+ &gamma;<sub><?phpphp echo($ir.",1");?></sub>,..., 
                   &mu;<sub><?phpphp echo($ir.",".$is);?></sub>= &mu;+ &alpha;<sub><?phpphp echo($ir);?></sub>+ &beta;<sub><?phpphp echo($is);?></sub>+ &gamma;<sub><?phpphp echo($ir.",".$is);?></sub>
</tr></table>  
&nbsp;&nbsp;&nbsp;&nbsp;&Sigma; &alpha;<sub>k</sub> = 0 , &nbsp;&nbsp;&nbsp;&Sigma; &beta;<sub>k</sub> = 0 , &nbsp;&nbsp;&nbsp;&Sigma; &gamma;<sub>k,l</sub> = 0
    <?phpphp for ($i =0; $i <$ir; $i++):?>
    <br><?phpphp for ($k =0; $k <$is; $k++): ?>
    X<sub><?phpphp echo(($i+1).",".($k+1).",1");?></sub>,...,X<sub><?phpphp echo(($i+1).",".($k+1).",".$ip);?></sub>:
      <?phpphp for ($j =0; $j <$ip; $j++): ?>
       <input type=double name="x[]" size="1" value="<?phpecho($x[$is*$ip*$i+$k*$ip+$j]);?>">
    <?phpphp endfor;?> &nbsp;&nbsp;&nbsp;&nbsp; <?phpphp endfor;endfor;?>
    <br>
    <?phpphp for ($i =0; $i <$ir; $i++):
          for ($k =0; $k <$is; $k++): 
          for ($j =0; $j <$ip; $j++):
              $xx[$i][$k][$j]=$x[$is*$ip*$i+$k*$ip+$j];
    endfor;endfor;endfor;?>
    <br> 

   
    null hypothesis &nbsp;&nbsp; H<sub>0</sub>: &alpha;<sub>1</sub> = ... = &alpha;<sub><?phpphp echo($ir);?></sub> = 0
    <br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
          H<sub>0</sub>: &beta;<sub>1</sub> = ... = &beta;<sub><?phpphp echo($is);?></sub> = 0
    <br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
          H<sub>0</sub>: &gamma;<sub>1,1</sub> = ... = &gamma;<sub><?phpphp echo($ir.",".$is);?></sub> = 0
    <br>
    <input type="submit" value="perform the test"> 
    <input type=hidden name=a value="2">
    </form>
<?phpphp 
endif;
break;

case 2: 


if($ir<2||$is<2||$ip<2||$ir>10||$is>10||$ip>10||!(round($ir)==$ir)||!(round($is)==$is)||!(round($ip)==$ip)): ?>
    <form method="get">  test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> number of categories A &nbsp;&nbsp; r: &nbsp; 
    <input type=integer size=1 name="ir" value="<?phpecho($ir);?>">  
    &nbsp;&nbsp;&nbsp;&nbsp; number of categories B &nbsp;&nbsp; s: &nbsp;
    <input type=integer size=1 name="is" value="<?phpecho($is);?>"> &nbsp; (enter integers from 2 to 10) ,
    &nbsp;&nbsp;&nbsp;&nbsp; range of categories &nbsp;&nbsp; p: &nbsp;
    <input type=integer size=1 name="ip" value="<?phpecho($ip);?>">      &nbsp; (enter an integer from 2 to 10)
    <input type="submit" value="yes">
    <input type=hidden name=a value="1">
  </form>

  <?phpphp echo("<font color=red> you did not enter required integers, correct</font>"); break;

else: ?>

    <form method="get">  test level &nbsp;&nbsp; &alpha; = 0,05 
    <br> number of categories A &nbsp;&nbsp; r: &nbsp; 
    <input type=integer size=1 name="ir" value="<?phpecho($ir);?>">  
    &nbsp;&nbsp;&nbsp;&nbsp; number of categories B &nbsp;&nbsp; s: &nbsp;
    <input type=integer size=1 name="is" value="<?phpecho($is);?>"> &nbsp; (enter integers from 2 to 10) ,
    &nbsp;&nbsp;&nbsp;&nbsp; range of categories &nbsp;&nbsp; p: &nbsp;
    <input type=integer size=1 name="ip" value="<?phpecho($ip);?>">      &nbsp; (enter an integer from 2 to 10)
    <input type="submit" value="yes">
  <br> random samples from &nbsp;
<table><tr>
N(&mu;<sub>1,1</sub>, &sigma;&sup2;) ,...,  N(&mu;<sub><?phpphp echo("1,".$is);?></sub>, &sigma;&sup2;)
</tr><tr>..............................................</tr>
<tr>
N(&mu;<sub><?phpphp echo($ir.",1");?></sub>, &sigma;&sup2;) ,...,  N(&mu;<sub><?phpphp echo($ir.",".$is);?></sub>, &sigma;&sup2;)
</tr></table>
&nbsp;&nbsp;&nbsp;
<table><tr>
 &mu;<sub>1,1</sub> = &mu; + &alpha;<sub>1</sub>+ &beta;<sub>1</sub>+ &gamma;<sub>1,1</sub>,..., 
                   &mu;<sub><?phpphp echo("1,".$is);?></sub>= &mu;+ &alpha;<sub>1</sub>+ &beta;<sub><?phpphp echo($is);?></sub>+ &gamma;<sub><?phpphp echo("1,".$is);?></sub>
</tr><tr> ...............................................................................</tr>
 &mu;<sub><?phpphp echo($ir.",1") ?></sub> = &mu; + &alpha;<sub><?phpphp echo($ir);?></sub>+ &beta;<sub>1</sub>+ &gamma;<sub><?phpphp echo($ir.",1");?></sub>,..., 
                   &mu;<sub><?phpphp echo($ir.",".$is);?></sub>= &mu;+ &alpha;<sub><?phpphp echo($ir);?></sub>+ &beta;<sub><?phpphp echo($is);?></sub>+ &gamma;<sub><?phpphp echo($ir.",".$is);?></sub>
</tr></table>   
&nbsp;&nbsp;&nbsp;&nbsp;&Sigma; &alpha;<sub>k</sub> = 0 , &nbsp;&nbsp;&nbsp;&Sigma; &beta;<sub>k</sub> = 0
    <?phpphp for ($i =0; $i <$ir; $i++):?>
    <br><?phpphp for ($k =0; $k <$is; $k++): ?>
    X<sub><?phpphp echo(($i+1).",".($k+1).",1");?></sub>,...,X<sub><?phpphp echo(($i+1).",".($k+1).",".$ip);?></sub>:
      <?phpphp for ($j =0; $j <$ip; $j++): ?>
       <input type=double name="x[]" size="1" value="<?phpecho($x[$is*$ip*$i+$k*$ip+$j]);?>">
    <?phpphp endfor;?> &nbsp;&nbsp;&nbsp;&nbsp; <?phpphp endfor;endfor;?>
    <br>

   
    null hypothesis &nbsp;&nbsp; H<sub>0</sub>: &alpha;<sub>1</sub> = ... = &alpha;<sub><?phpphp echo($ir);?></sub> = 0
    <br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
         H<sub>0</sub>: &beta;<sub>1</sub> = ... = &beta;<sub><?phpphp echo($is);?></sub> = 0
    <br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
         H<sub>0</sub>: &gamma;<sub>1,1</sub> = ... = &gamma;<sub><?phpphp echo($ir.",".$is);?></sub> = 0
    <br>
    <?phpphp for ($i =0; $i <$ir; $i++):
          for ($k =0; $k <$is; $k++): 
          for ($j =0; $j <$ip; $j++):
              $xx[$i][$k][$j]=$x[$is*$ip*$i+$k*$ip+$j];
    endfor;endfor;endfor;?>
    <br> 
 <input type="submit" value="perform the  test">     
 <input type=hidden name=a value="2">
    </form>


    <?phpphp 
$nnn=$ir*$is*$ip;
for ($i =0; $i <$ir; $i++):
for ($j =0; $j <$is; $j++):
 $mx[$i][$j]=sum($xx[$i][$j])/$ip; $zmx[$i][$j]=zaokr($mx[$i][$j],4); 
    endfor;endfor;
for ($i =0; $i <$ir; $i++):
 $mma[$i]=sum($mx[$i])/$is; $zmma[$i]=zaokr($mma[$i],4);
    endfor;
for ($i =0; $i <$is; $i++):
for ($j =0; $j <$ir; $j++):
$mmx[$i][$j]=$mx[$j][$i];
    endfor;endfor;
for ($j =0; $j <$is; $j++):
 $mmb[$j]=sum($mmx[$j])/$ir; $zmmb[$j]=zaokr($mmb[$j],4);
    endfor;
$mmm=sum($x)/$nnn; $zmmm=zaokr($mmm,4);
$st=sctv($x,$mmm); $zst=zaokr($st,4);
$sa=$is*$ip*sctv($mma,$mmm); $zsa=zaokr($sa,4);
$sb=$ir*$ip*sctv($mmb,$mmm); $zsb=zaokr($sb,4);
$ft=$nnn-1;
$fa=$ir-1;
$fb=$is-1;
$se=0;
for ($i =0; $i <$ir; $i++):
for ($j =0; $j <$is; $j++):
for ($k =0; $k <$ip; $k++):
$se=$se+pow($xx[$i][$j][$k]-$mx[$i][$j],2);
endfor;endfor;endfor; $zse=zaokr($se,4);
$fe=$nnn-$ir*$is;
$ss=$se/$fe;  $zss=zaokr($ss,4);
$sab=$st-$sa-$sb-$se; $zsab=zaokr($sab,4);
$fab=($ir-1)*($is-1);
?>
    <span style="text-decoration: overline">X</span>  = <?phpphp echo($zmmm);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   
    <?phpphp for ($i =0; $i <$ir; $i++): ?>
      <span style="text-decoration: overline">X</span><sup>A</sup><sub><?phpphp echo(($i+1)); ?></sub> = <?phpphp echo($zmma[$i]);?> &nbsp;&nbsp;&nbsp;&nbsp; 
    <?phpphp endfor; ?>
    <?phpphp for ($i =0; $i <$is; $i++): ?>
      <span style="text-decoration: overline">X</span><sup>B</sup><sub><?phpphp echo(($i+1)); ?></sub> = <?phpphp echo($zmmb[$i]);?> &nbsp;&nbsp;&nbsp;&nbsp; 
    <?phpphp endfor; ?>

<table><tr>
  <?phpphp for ($i =0; $i <$ir; $i++):?>
<tr>
<?phpphp for ($j =0; $j <$is; $j++):?>
<td><span style="text-decoration: overline">X</span><sub><?phpphp echo(($i+1).",".($j+1));?></sub>  = <?phpphp echo($zmx[$i][$j]);?>&nbsp;&nbsp;&nbsp;</td>
<?phpphp endfor;?>
</tr>
<?phpphp endfor;?>
</table> 

    <br> 
    S<sub>A</sub> = <?phpphp echo($zsa);?> &nbsp;&nbsp;&nbsp; 
    f<sub>A</sub> = <?phpphp echo($fa);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
    S<sub>B</sub> = <?phpphp echo($zsb);?> &nbsp;&nbsp;&nbsp; 
    f<sub>B</sub> = <?phpphp echo($fb);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    S<sub>AB</sub> = <?phpphp echo($zsab);?> &nbsp;&nbsp;&nbsp; 
    f<sub>AB</sub> = <?phpphp echo($fab);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 S<sub>e</sub> = <?phpphp echo($zse);?> &nbsp;&nbsp;&nbsp; 
 f<sub>e</sub> = <?phpphp echo($fe);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
 s &sup2; = <?phpphp echo($zss);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
S<sub>T</sub> = <?phpphp echo($zst);?> &nbsp;&nbsp;&nbsp; 
 f<sub>T</sub> = <?phpphp echo($ft);?>


<?phpphp  if ($ss==0):?> 
    <br> <font color=red> residual sum of squares is equal to  0, this test cannot be used </font>
  <?phpphp 
  else:  

$ffa=$sa/$fa/$ss; 
$faz=zaokr($ffa,3);
$finva=invf($fa,$fe); 
$ffb=$sb/$fb/$ss; 
$fbz=zaokr($ffb,3);
$finvb=invf($fb,$fe); 
$ffab=$sab/$fab/$ss; 
$fabz=zaokr($ffab,3);
$finvab=invf($fab,$fe); 
?>
    
<br> 
F<sub>A</sub> = <?phpphp echo($faz);?> &nbsp;&nbsp;&nbsp;&nbsp;
F<sub><?phpphp echo($fa.",".$fe)?></sub> <?phpphp echo("(0.95) = ".$finva);?>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
F<sub>B</sub> = <?phpphp echo($fbz);?> &nbsp;&nbsp;&nbsp;&nbsp;
F<sub><?phpphp echo($fb.",".$fe)?></sub> <?phpphp echo("(0.95) = ".$finvb);?>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
F<sub>AB</sub> = <?phpphp echo($fabz);?> &nbsp;&nbsp;&nbsp;&nbsp;
F<sub><?phpphp echo($fab.",".$fe)?></sub> <?phpphp echo("(0.95) = ".$finvab);?>
    <br>
  <?phpphp 
    if($ffa>=$finva): ?> 
      F<sub>A</sub> &ge;   F<sub><?phpphp echo($fa.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;        
hypothesis &nbsp;&nbsp; H<sub>0</sub> : &alpha;<sub>1</sub> = ... = &alpha;<sub><?phpphp echo($ir); ?></sub> = 0  
         &nbsp;&nbsp;is rejected
    <?phpphp 
    else: ?> 
      F<sub>A</sub> <   F<sub><?phpphp echo($fa.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;        
hypothesis &nbsp;&nbsp; H<sub>0</sub> : &alpha;<sub>1</sub> = ... = &alpha;<sub><?phpphp echo($ir); ?></sub> = 0  
        &nbsp;&nbsp;is not rejected
    <?phpphp 
    endif; ?>
    <br>
  <?phpphp 
    if($ffb>=$finvb): ?> 
      F<sub>B</sub> &ge;   F<sub><?phpphp echo($fb.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
       hypothesis &nbsp;&nbsp; H<sub>0</sub> : &beta;<sub>1</sub> = ... = &beta;<sub><?phpphp echo($is); ?></sub> = 0  
         &nbsp;&nbsp;is rejected
    <?phpphp 
    else: ?>
      F<sub>B</sub> <   F<sub><?phpphp echo($fb.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  
       hypothesis &nbsp;&nbsp; H<sub>0</sub> : &beta;<sub>1</sub> = ... = &beta;<sub><?phpphp echo($is); ?></sub> = 0  
        &nbsp;&nbsp;is not rejected
    <?phpphp 
    endif; ?>
    <br>
  <?phpphp 
    if($ffab>=$finvab): ?> 
      F<sub>AB</sub> &ge;   F<sub><?phpphp echo($fab.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;        
hypothesis &nbsp;&nbsp; H<sub>0</sub> : &gamma;<sub>1,1</sub> = ... = &gamma;<sub><?phpphp echo($ir.",".$is); ?></sub> = 0  
         &nbsp;&nbsp;is rejected
    <?phpphp 
    else: ?> 
      F<sub>AB</sub> <   F<sub><?phpphp echo($fab.",".$fe)?></sub>(0.95) 
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
       hypothesis &nbsp;&nbsp; H<sub>0</sub> : &gamma;<sub>1,1</sub> = ... = &gamma;<sub><?phpphp echo($ir.",".$is); ?></sub> = 0    
        &nbsp;&nbsp;is not rejected
    <?phpphp     endif; ?>
<?phpphp   endif; ?> 

<br><br>
<?phpphp
echo'<a href="Eanovadvoj.php?ir=',$ir,'&  is=',$is,'& ip=',$ip,'&';
for ($i =0; $i <$ir; $i++): 
for ($k =0; $k <$is; $k++): 
for ($j =0; $j <$ip; $j++): 
echo'x%5B%5D=',$x[$is*$ip*$i+$ip*$k+$j],'&';
endfor;endfor;endfor;
echo'aa=',$aa,'& a=1"> Two-way ANOVA without interaction </a>';
?>

    <form>
    <input type="submit" value="new entry"> 
    <input type=hidden name=a value="0">
    </form>
<?phpphp 
endif;
break;
endswitch;?>

</body>
</html>



 