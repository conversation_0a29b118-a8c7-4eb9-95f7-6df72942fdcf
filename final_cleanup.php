<?php
/**
 * Final cleanup script for PHP 8.3 compatibility
 */

function finalCleanup($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix double PHP tags
    $content = preg_replace('/<\?phpphp/', '<?php', $content);
    $content = preg_replace('/<\?php\s*php/', '<?php', $content);
    
    // Fix short tags that might have been missed
    $content = preg_replace('/<\?\s+/', '<?php ', $content);
    
    // Fix any remaining unquoted attributes
    $content = preg_replace('/(\s)(align|width|height|size|colspan|rowspan)=([^"\s>]+)/', '$1$2="$3"', $content);
    
    // Fix any remaining echo statements
    $content = preg_replace('/<\?\s*echo\s*\(([^)]+)\)\s*;\s*\?>/', '<?php echo($1); ?>', $content);
    
    // Fix language attributes for Czech files
    if (strpos($content, 'TESTOVÁNÍ') !== false || strpos($content, 'Jednovýběrový') !== false) {
        $content = preg_replace('/<html>/', '<html lang="cs">', $content);
    } elseif (strpos($content, 'HYPOTHESIS') !== false || strpos($content, 'One-sample') !== false) {
        $content = preg_replace('/<html>/', '<html lang="en">', $content);
    }
    
    // Add titles where missing
    if (preg_match('/<head>/', $content) && !preg_match('/<title>/', $content)) {
        if (strpos($content, 'TESTOVÁNÍ') !== false) {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Testování hypotéz</title>', $content);
        } elseif (strpos($content, 'HYPOTHESIS') !== false) {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Hypothesis Testing</title>', $content);
        } else {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Statistické testy</title>', $content);
        }
    }
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Final cleanup: $filePath\n";
        return true;
    }
    
    echo "No final cleanup needed: $filePath\n";
    return false;
}

// Get all PHP files
$phpFiles = glob('statisticsonweb.tf.czu.cz/*.php');

echo "Found " . count($phpFiles) . " PHP files\n";
echo "Starting final cleanup...\n\n";

$fixedCount = 0;
foreach ($phpFiles as $file) {
    if (finalCleanup($file)) {
        $fixedCount++;
    }
}

echo "\nCompleted final cleanup! Fixed $fixedCount files.\n";
?>
